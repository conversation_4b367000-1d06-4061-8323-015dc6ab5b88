import os
import json
import logging
import sys
from logging.handlers import RotatingFileHandler
from db.database import Database
from config.yaml_config_manager import YAMLConfigManager


class TenantAwareFormatter(logging.Formatter):
    """Custom formatter that includes tenant name in log messages"""

    def __init__(self, fmt=None, tenant_name="GLOBAL"):
        super().__init__(fmt)
        self.tenant_name = tenant_name

    def format(self, record):
        # Add tenant_name to the log record
        record.tenant_name = self.tenant_name
        return super().format(record)

class Config:
    """Configuration manager for the Telegram bot"""

    def __init__(self, config_path=None, tenant_name=None):
        """
        Initialize configuration manager.

        Args:
            config_path: Path to configuration file
            tenant_name: Optional tenant name for multi-tenant mode
        """
        # Use default path if not provided
        self.config_path = config_path or './config/config.json'
        self.db = None
        self.tenant_name = tenant_name

        # Load basic config for database connection and logging
        self.load_config()
        self.setup_logging()

        # Initialize YAML configuration manager for customer-specific configs
        self.yaml_config = YAMLConfigManager(logger=self.logger)

        # IMPORTANT: Always load bot token and channel_id from MongoDB
        # This will override any values from the config file
        self.load_bot_config_from_db()

    def load_config(self):
        """Load basic configuration from JSON file
        Note: bot_token and channel_id will be loaded from MongoDB, not from this file
        """
        try:
            with open(self.config_path, 'r') as f:
                self.config = json.load(f)
                print("Config file loaded - note that bot_token and channel_id will be loaded from MongoDB")
        except (FileNotFoundError, json.JSONDecodeError):
            # Create default config
            self.config = {
                # These are just placeholders and will be overridden by MongoDB values
                "bot_token": "PLACEHOLDER_WILL_BE_LOADED_FROM_MONGODB",
                "channel_id": "PLACEHOLDER_WILL_BE_LOADED_FROM_MONGODB",
                "admin_ids": [],  # Add admin user IDs here
                "data_paths": {
                    "subscriptions": "./data/subscriptions.json",
                    "access_codes": "./data/access_codes.txt"
                },
                "logging": {
                    "log_file": "./logs/bot.log",
                    "level": "INFO",
                    "format": "%(asctime)s - %(name)s - %(tenant_name)s - %(levelname)s - %(message)s",
                    "max_size_mb": 5,
                    "backup_count": 3
                },
                "database": {
                    "mongo_uri": "mongodb://localhost:27017/",
                    "db_name": "admin_panel"
                }
            }
            self.save_config()
            print("Sample config file created. Please ensure MongoDB is configured with bot token and channel ID.")
            # Don't exit here, let the MongoDB check handle that

    def save_config(self):
        """Save configuration to JSON file"""
        os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
        with open(self.config_path, 'w') as f:
            json.dump(self.config, f, indent=4)

    def get(self, key, default=None):
        """Get a configuration value"""
        return self.config.get(key, default)

    def setup_logging(self):
        """Set up tenant-aware logging based on configuration"""
        log_config = self.config.get('logging', {})
        log_file = log_config.get('log_file', '/Users/<USER>/Documents/aws/project/logs/bot.log')
        log_level_str = log_config.get('level', 'INFO')
        # Updated format to include tenant name
        log_format = log_config.get('format', '%(asctime)s - %(name)s - %(tenant_name)s - %(levelname)s - %(message)s')
        max_size_mb = log_config.get('max_size_mb', 5)
        backup_count = log_config.get('backup_count', 3)

        # Convert string log level to logging constant
        log_level = getattr(logging, log_level_str.upper(), logging.INFO)

        # Create logs directory if it doesn't exist
        os.makedirs(os.path.dirname(log_file), exist_ok=True)

        # Create tenant-specific logger name
        logger_name = f"telegram_bot.{self.tenant_name}" if self.tenant_name else "telegram_bot"
        logger = logging.getLogger(logger_name)
        logger.setLevel(log_level)

        # Remove existing handlers to avoid duplicates
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # Create file handler for logging to file
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=max_size_mb * 1024 * 1024,
            backupCount=backup_count
        )
        file_handler.setLevel(log_level)

        # Create console handler for logging to console
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)

        # Create tenant-aware formatter
        formatter = TenantAwareFormatter(log_format, self.tenant_name or "GLOBAL")
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # Add handlers to logger
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        # Add filter to mask sensitive information
        class SensitiveDataFilter(logging.Filter):
            def __init__(self, token):
                super().__init__()
                self.token = token
                # Only apply filter if token is valid and not a placeholder
                self.should_filter = token and not token.startswith('PLACEHOLDER')

            def filter(self, record):
                # Only apply filtering if we have a valid token
                if not self.should_filter:
                    return True

                if hasattr(record, 'msg') and isinstance(record.msg, str):
                    # Only mask the actual bot token, not every character
                    if self.token in record.msg:
                        record.msg = record.msg.replace(self.token, "[REDACTED_TOKEN]")

                # Also check args for token
                if hasattr(record, 'args') and record.args:
                    args_list = list(record.args)
                    for i, arg in enumerate(args_list):
                        if isinstance(arg, str) and self.token in arg:
                            args_list[i] = arg.replace(self.token, "[REDACTED_TOKEN]")
                    record.args = tuple(args_list)

                return True

        # Note: At this point, bot_token might still be a placeholder
        # The actual token will be loaded from MongoDB later
        # We'll update the filter after loading from MongoDB
        self.sensitive_filter = SensitiveDataFilter(self.config.get('bot_token', ''))
        logger.addFilter(self.sensitive_filter)

        # Disable httpx logger completely
        httpx_logger = logging.getLogger('httpx')
        httpx_logger.setLevel(logging.CRITICAL)  # Only log critical errors
        httpx_logger.propagate = False  # Don't propagate to root logger

        # Disable telegram.ext logger for network operations
        telegram_network_logger = logging.getLogger('telegram.ext.utils.networkloop')
        telegram_network_logger.setLevel(logging.WARNING)  # Only log warnings and above

        # Store logger in instance for easy access
        self.logger = logger

        # Log initialization
        logger.info("Logging initialized with level: %s", log_level_str)

    def get_logger(self):
        """Get the configured logger"""
        return self.logger

    def get_database_path(self):
        """Get the path to the SQLite database"""
        return self.config.get('database', {}).get('path', '/Users/<USER>/Documents/aws/project/db/admin_panels.db')

    def load_bot_config_from_db(self):
        """Load bot token and channel_id from MongoDB telegram_bots collection
        This is the ONLY source of bot token and channel_id - config file values are ignored

        In multi-tenant mode, loads from the specified tenant database.
        """
        try:
            # Initialize database connection if not already done
            # In MongoDB, we can't use 'if not self.db' as database objects don't support boolean evaluation
            if self.db is None:
                # Get MongoDB URI from config.json
                mongo_uri = self.config.get('database', {}).get('mongo_uri')

                # Create database connection with tenant name if in multi-tenant mode
                if self.tenant_name:
                    self.db = Database(mongo_uri, tenant_name=self.tenant_name)
                else:
                    self.db = Database(mongo_uri)

            # Get logger (should be initialized by now)
            logger = self.get_logger()

            if self.tenant_name:
                logger.info(f"Loading bot configuration from tenant database: {self.tenant_name}_custdb")
            else:
                logger.info("Loading bot configuration from MongoDB - config file values will be ignored")

            # Try to get bot info from MongoDB
            try:
                bot_info = self.db.get_telegram_bot_info()
                if not bot_info:
                    self.tenant_name
                    raise Exception(f"ERROR: No bot information found in MongoDB telegram_bots collection for tenant {self.tenant_name}")
            except Exception as e:
                logger.error(e)
                return False

            # Check if token exists in bot_info
            if 'token' not in bot_info:
                error_msg = f"ERROR: Bot token not found in MongoDB telegram_bots collection"
                if self.tenant_name:
                    error_msg += f" for tenant {self.tenant_name}"
                error_msg += ". Exiting."
                logger.error(error_msg)
                print(error_msg)
                sys.exit(1)

            # Check if channel_id exists in bot_info
            if 'channel_id' not in bot_info:
                error_msg = f"ERROR: Channel ID not found in MongoDB telegram_bots collection"
                if self.tenant_name:
                    error_msg += f" for tenant {self.tenant_name}"
                error_msg += ". Exiting."
                logger.error(error_msg)
                print(error_msg)
                sys.exit(1)

            # Set the values from MongoDB
            self.config['bot_token'] = bot_info['token']
            self.config['channel_id'] = bot_info['channel_id']

            # Store tenant-specific information if available
            if 'tenant_name' in bot_info:
                self.config['tenant_name'] = bot_info['tenant_name']
            elif self.tenant_name:
                self.config['tenant_name'] = self.tenant_name

            # Log the success
            tenant_info = f" for tenant {self.tenant_name}" if self.tenant_name else ""
            logger.info(f"Successfully loaded bot token from MongoDB{tenant_info} for bot: {bot_info.get('name', 'Unknown')}")
            logger.info(f"Successfully loaded channel ID from MongoDB{tenant_info}: {bot_info['channel_id']}")

            # Update the sensitive filter with the actual token from MongoDB
            if hasattr(self, 'sensitive_filter'):
                # Remove the old filter
                logger.removeFilter(self.sensitive_filter)
                # Create a new filter with the actual token
                # Define the filter class locally to avoid scope issues
                class SensitiveDataFilter(logging.Filter):
                    def __init__(self, token):
                        super().__init__()
                        self.token = token
                        # Only apply filter if token is valid and not the default
                        self.should_filter = token and not token.startswith('PLACEHOLDER')

                    def filter(self, record):
                        # Only apply filtering if we have a valid token
                        if not self.should_filter:
                            return True

                        if hasattr(record, 'msg') and isinstance(record.msg, str):
                            # Only mask the actual bot token, not every character
                            if self.token in record.msg:
                                record.msg = record.msg.replace(self.token, "[REDACTED_TOKEN]")

                        # Also check args for token
                        if hasattr(record, 'args') and record.args:
                            args_list = list(record.args)
                            for i, arg in enumerate(args_list):
                                if isinstance(arg, str) and self.token in arg:
                                    args_list[i] = arg.replace(self.token, "[REDACTED_TOKEN]")
                            record.args = tuple(args_list)

                        return True

                self.sensitive_filter = SensitiveDataFilter(self.config.get('bot_token', ''))
                # Add the new filter
                logger.addFilter(self.sensitive_filter)
                logger.info("Updated sensitive data filter with token from MongoDB")

            # Double-check that we have a valid bot token
            if not self.config.get('bot_token') or len(self.config.get('bot_token', '')) < 10:
                error_msg = "ERROR: Invalid bot token found in MongoDB (too short). Exiting."
                logger.error(error_msg)
                print(error_msg)
                sys.exit(1)

            # Double-check that we have a valid channel ID
            if not self.config.get('channel_id'):
                error_msg = "ERROR: Invalid channel ID found in MongoDB (empty). Exiting."
                logger.error(error_msg)
                print(error_msg)
                sys.exit(1)

        except Exception as e:
            # If there's an error, log it and exit
            error_msg = f"ERROR: Failed to load bot configuration from MongoDB: {e}. Exiting."
            print(error_msg)
            sys.exit(1)

    def get_tenant_name(self):
        """
        Get the name of the current tenant.

        Returns:
            Tenant name or None if in single-tenant mode
        """
        return self.tenant_name or self.config.get('tenant_name')

    @classmethod
    def create_for_tenant(cls, tenant_name, config_path=None):
        """
        Create a Config instance for a specific tenant.

        Args:
            tenant_name: Name of the tenant
            config_path: Optional path to configuration file

        Returns:
            Config instance for the specified tenant
        """
        return cls(config_path=config_path, tenant_name=tenant_name)

    @classmethod
    def create_for_all_tenants(cls, config_path=None, mongo_uri=None):
        """
        Create Config instances for all available tenants.

        Args:
            config_path: Optional path to configuration file
            mongo_uri: Optional MongoDB URI

        Returns:
            Dictionary mapping tenant names to Config instances
        """
        # Get all tenant names
        tenant_names = Database.get_all_tenant_names(mongo_uri)

        # Create Config instances for each tenant
        configs = {}
        for tenant_name in tenant_names:
            try:
                configs[tenant_name] = cls.create_for_tenant(tenant_name, config_path)
                if configs[tenant_name].get("bot_token") == "YOUR_BOT_TOKEN_HERE":
                    configs.pop(tenant_name)
                    raise Exception("Bot Token not found")
            except Exception as e:
                print(f"Warning: Could not create config for tenant {tenant_name}: {e}")

        return configs

    # YAML Configuration Methods
    def get_yaml_config(self, menu_name=None):
        """
        Get YAML configuration for the current tenant.

        Args:
            menu_name: Optional specific menu name to get

        Returns:
            Configuration dictionary or menu-specific configuration
        """
        if not self.tenant_name:
            self.logger.warning("No tenant name available for YAML config lookup")
            return {}

        if menu_name:
            return self.yaml_config.get_menu_config(self.tenant_name, menu_name)
        else:
            return self.yaml_config.load_customer_config(self.tenant_name)

    def get_menu_text(self, menu_name):
        """
        Get message text for a specific menu from YAML config.

        Args:
            menu_name: Name of the menu

        Returns:
            Message text string
        """
        if not self.tenant_name:
            return ""
        return self.yaml_config.get_message_text(self.tenant_name, menu_name)

    def get_menu_buttons(self, menu_name):
        """
        Get button configuration for a specific menu from YAML config.

        Args:
            menu_name: Name of the menu

        Returns:
            List of button configuration dictionaries
        """
        if not self.tenant_name:
            return []
        return self.yaml_config.get_button_config(self.tenant_name, menu_name)

    def get_image_path(self, image_name):
        """
        Get image path from YAML config.

        Args:
            image_name: Name of the image

        Returns:
            Image path string or None if not found
        """
        if not self.tenant_name:
            return None
        return self.yaml_config.get_image_path(self.tenant_name, image_name)

    def get_external_link(self, link_name):
        """
        Get external link from YAML config.

        Args:
            link_name: Name of the link

        Returns:
            Link URL string or None if not found
        """
        if not self.tenant_name:
            return None
        return self.yaml_config.get_external_link(self.tenant_name, link_name)

    def get_form_prompt(self, prompt_name):
        """
        Get form prompt text from YAML config.

        Args:
            prompt_name: Name of the prompt

        Returns:
            Prompt text string
        """
        if not self.tenant_name:
            return f"Please enter {prompt_name}:"
        return self.yaml_config.get_form_prompt(self.tenant_name, prompt_name)

    def get_error_message(self, error_type):
        """
        Get error message from YAML config.

        Args:
            error_type: Type of error

        Returns:
            Error message string
        """
        if not self.tenant_name:
            return "Sorry, something went wrong. Please try again later."
        return self.yaml_config.get_error_message(self.tenant_name, error_type)
