"""
YAML Configuration Manager for Telegram Bot
Handles loading and managing per-customer YAML configuration files
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path


class YAMLConfigManager:
    """
    Manages YAML-based configuration for per-customer bot settings.
    
    This class handles:
    - Loading customer-specific YAML configuration files
    - Providing fallback to default configurations
    - Validating configuration structure
    - Caching configurations for performance
    """
    
    def __init__(self, config_base_path: str = "custom_menu", logger: Optional[logging.Logger] = None):
        """
        Initialize the YAML configuration manager.
        
        Args:
            config_base_path: Base path where customer config folders are located
            logger: Optional logger instance
        """
        self.config_base_path = Path(config_base_path)
        self.logger = logger or logging.getLogger(__name__)
        self._config_cache = {}  # Cache for loaded configurations
        self._default_config = None
        
        # Ensure the config base path exists
        self.config_base_path.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"YAMLConfigManager initialized with base path: {self.config_base_path}")
    
    def load_customer_config(self, customer_name: str) -> Dict[str, Any]:
        """
        Load configuration for a specific customer.
        
        Args:
            customer_name: Name of the customer
            
        Returns:
            Dictionary containing the customer's configuration
        """
        # Check cache first
        if customer_name in self._config_cache:
            self.logger.debug(f"Returning cached config for customer: {customer_name}")
            return self._config_cache[customer_name]
        
        # Construct path to customer config file
        config_file_path = self.config_base_path / customer_name / f"{customer_name}.yaml"
        
        try:
            if config_file_path.exists():
                self.logger.info(f"Loading YAML config for customer: {customer_name}")
                with open(config_file_path, 'r', encoding='utf-8') as file:
                    config = yaml.safe_load(file)
                
                # Validate the configuration
                if self._validate_config(config, customer_name):
                    # Cache the configuration
                    self._config_cache[customer_name] = config
                    self.logger.info(f"Successfully loaded and cached config for customer: {customer_name}")
                    return config
                else:
                    self.logger.error(f"Invalid configuration for customer: {customer_name}")
                    return self._get_default_config()
            else:
                self.logger.warning(f"Config file not found for customer: {customer_name}, using default config")
                return self._get_default_config()
                
        except yaml.YAMLError as e:
            self.logger.error(f"YAML parsing error for customer {customer_name}: {e}")
            return self._get_default_config()
        except Exception as e:
            self.logger.error(f"Error loading config for customer {customer_name}: {e}")
            return self._get_default_config()
    
    def _validate_config(self, config: Dict[str, Any], customer_name: str) -> bool:
        """
        Validate the structure of a configuration dictionary.
        
        Args:
            config: Configuration dictionary to validate
            customer_name: Name of the customer (for logging)
            
        Returns:
            True if configuration is valid, False otherwise
        """
        required_sections = ['main_menu', 'bot_info']
        
        for section in required_sections:
            if section not in config:
                self.logger.error(f"Missing required section '{section}' in config for customer: {customer_name}")
                return False
        
        # Validate main_menu structure
        main_menu = config.get('main_menu', {})
        if 'title' not in main_menu or 'buttons' not in main_menu:
            self.logger.error(f"Invalid main_menu structure for customer: {customer_name}")
            return False
        
        # Validate buttons structure
        buttons = main_menu.get('buttons', [])
        if not isinstance(buttons, list) or len(buttons) == 0:
            self.logger.error(f"Invalid buttons structure in main_menu for customer: {customer_name}")
            return False
        
        for button in buttons:
            if not isinstance(button, dict) or 'text' not in button or 'callback_data' not in button:
                self.logger.error(f"Invalid button structure in main_menu for customer: {customer_name}")
                return False
        
        self.logger.debug(f"Configuration validation passed for customer: {customer_name}")
        return True
    
    def _get_default_config(self) -> Dict[str, Any]:
        """
        Get the default configuration as fallback.
        
        Returns:
            Default configuration dictionary
        """
        if self._default_config is None:
            self._default_config = self._create_default_config()
        
        return self._default_config.copy()
    
    def _create_default_config(self) -> Dict[str, Any]:
        """
        Create the default configuration structure.
        
        Returns:
            Default configuration dictionary
        """
        return {
            'bot_info': {
                'name': 'Default Bot',
                'description': 'Default Telegram Bot Configuration',
                'welcome_message': 'Welcome to the verification Junction ✅ \n\nUnlock the potential of expert-led Forex trading world 📈'
            },
            'main_menu': {
                'title': 'Welcome to the verification Junction ✅ \n\nUnlock the potential of expert-led Forex trading world 📈',
                'buttons': [
                    {'text': 'New Registration 🧑‍💻', 'callback_data': 'new_registration'},
                    {'text': 'Claim Membership 🎖️', 'callback_data': 'claim_membership'},
                    {'text': 'Verify Membership 🔃', 'callback_data': 'verify_membership'},
                    {'text': 'Withdrawals & Deposits 💱', 'callback_data': 'wd_start'},
                    {'text': 'Submit a Request 📪', 'callback_data': 'submit_request_start'}
                ]
            },
            'new_registration': {
                'title': 'Unlock seamless benefits of joining the 0-cost membership, Get access to :\n\nDaily Setups and Highly Accurate Signals 🎯\nChart Updates 📊\nExclusive Forex Trading Course 📹\nNews info & Data releases 📰\nPriority to your Account-related issues 🥇\nPremium support on Withdrawals/Deposits & Trades 🤝',
                'buttons': [
                    {'text': 'I Want to Join 🏌️', 'callback_data': 'reg_want_to_join'},
                    {'text': 'Account created ✅', 'callback_data': 'account_created'},
                    {'text': 'I Already have an Account 🧾', 'callback_data': 'partner_change_start'},
                    {'text': 'Need more Help 🪃', 'callback_data': 'submit_request_start'},
                    {'text': 'Back to Main Menu', 'callback_data': 'back_to_menu'}
                ]
            },
            'images': {
                'welcome': 'assets/welcome.webp',
                'membership_offer': 'assets/membership_offer.webp',
                'claim_membership': 'assets/claim_membership.webp',
                'verify_membership': 'assets/verify_membership.webp',
                'withdrawal': 'assets/withdrawal.webp',
                'support': 'assets/support.webp',
                'account_number': 'assets/account_number_image.png'
            },
            'external_links': {
                'whatsapp_support': 'http://wa.me/+************',
                'withdrawal_team': 'http://wa.me/+************'
            },
            'form_prompts': {
                'account_number': 'Enter the account number that is available in your trading profile:',
                'email': 'Please enter your email address:',
                'whatsapp': 'Please enter your WhatsApp number:',
                'name': 'Please enter your full name:',
                'trading_experience': 'Please enter your trading experience:',
                'request_text': 'Please describe your request or issue:'
            },
            'error_messages': {
                'general_error': 'Sorry, something went wrong. Please try again later.',
                'invalid_code': 'Invalid access code. Please try again.',
                'account_not_found': 'Account not found in our system.',
                'pdf_not_found': 'PDF file not available at the moment.'
            }
        }
    
    def get_menu_config(self, customer_name: str, menu_name: str) -> Dict[str, Any]:
        """
        Get configuration for a specific menu.
        
        Args:
            customer_name: Name of the customer
            menu_name: Name of the menu (e.g., 'main_menu', 'new_registration')
            
        Returns:
            Menu configuration dictionary
        """
        config = self.load_customer_config(customer_name)
        return config.get(menu_name, {})
    
    def get_button_config(self, customer_name: str, menu_name: str) -> List[Dict[str, Any]]:
        """
        Get button configuration for a specific menu.
        
        Args:
            customer_name: Name of the customer
            menu_name: Name of the menu
            
        Returns:
            List of button configuration dictionaries
        """
        menu_config = self.get_menu_config(customer_name, menu_name)
        return menu_config.get('buttons', [])
    
    def get_message_text(self, customer_name: str, menu_name: str) -> str:
        """
        Get message text for a specific menu.
        
        Args:
            customer_name: Name of the customer
            menu_name: Name of the menu
            
        Returns:
            Message text string
        """
        menu_config = self.get_menu_config(customer_name, menu_name)
        return menu_config.get('title', '')
    
    def get_image_path(self, customer_name: str, image_name: str) -> Optional[str]:
        """
        Get image path for a specific image.
        
        Args:
            customer_name: Name of the customer
            image_name: Name of the image
            
        Returns:
            Image path string or None if not found
        """
        config = self.load_customer_config(customer_name)
        images = config.get('images', {})
        return images.get(image_name)
    
    def get_external_link(self, customer_name: str, link_name: str) -> Optional[str]:
        """
        Get external link for a specific link name.
        
        Args:
            customer_name: Name of the customer
            link_name: Name of the link
            
        Returns:
            Link URL string or None if not found
        """
        config = self.load_customer_config(customer_name)
        links = config.get('external_links', {})
        return links.get(link_name)
    
    def get_form_prompt(self, customer_name: str, prompt_name: str) -> str:
        """
        Get form prompt text.
        
        Args:
            customer_name: Name of the customer
            prompt_name: Name of the prompt
            
        Returns:
            Prompt text string
        """
        config = self.load_customer_config(customer_name)
        prompts = config.get('form_prompts', {})
        return prompts.get(prompt_name, f'Please enter {prompt_name}:')
    
    def get_error_message(self, customer_name: str, error_type: str) -> str:
        """
        Get error message text.
        
        Args:
            customer_name: Name of the customer
            error_type: Type of error
            
        Returns:
            Error message string
        """
        config = self.load_customer_config(customer_name)
        errors = config.get('error_messages', {})
        return errors.get(error_type, 'Sorry, something went wrong. Please try again later.')
    
    def clear_cache(self, customer_name: Optional[str] = None):
        """
        Clear configuration cache.
        
        Args:
            customer_name: Optional specific customer to clear, or None to clear all
        """
        if customer_name:
            self._config_cache.pop(customer_name, None)
            self.logger.info(f"Cleared cache for customer: {customer_name}")
        else:
            self._config_cache.clear()
            self.logger.info("Cleared all configuration cache")
    
    def reload_config(self, customer_name: str) -> Dict[str, Any]:
        """
        Force reload configuration for a customer.
        
        Args:
            customer_name: Name of the customer
            
        Returns:
            Reloaded configuration dictionary
        """
        self.clear_cache(customer_name)
        return self.load_customer_config(customer_name)
