# Tenant-Aware Logging Implementation

## Overview
This document describes the implementation of tenant-aware logging throughout the Telegram bot application. Every log message now includes the tenant name, making it easy to identify which tenant's operations are being logged.

## Log Format
All log messages now follow this format:
```
YYYY-MM-DD HH:MM:SS,mmm - logger_name - TENANT_NAME - LEVEL - message
```

### Examples:
```
2025-05-28 06:20:59,960 - root - CUSTOMER - INFO - Finished processing for user 635834411
2025-05-28 06:20:59,960 - root - INDX - INFO - Finished processing for user 635834411
2025-05-28 06:20:59,960 - root - RISHUX - INFO - Finished processing for user 635834411
```

## Implementation Details

### 1. TenantAwareFormatter Class
Created a custom logging formatter that automatically includes tenant names in log records:

**Location**: `config/config.py` and `src/utils/logging_utils.py`

```python
class TenantAwareFormatter(logging.Formatter):
    """Custom formatter that includes tenant name in log messages"""
    
    def __init__(self, fmt=None, tenant_name="GLOBAL"):
        super().__init__(fmt)
        self.tenant_name = tenant_name
    
    def format(self, record):
        # Add tenant_name to the log record
        record.tenant_name = self.tenant_name
        return super().format(record)
```

### 2. Config Class Updates
Modified the `Config` class to use tenant-aware logging:

- **Updated logging format**: Changed default format to include `%(tenant_name)s`
- **Tenant-specific loggers**: Each tenant gets its own logger namespace
- **Automatic formatter application**: All handlers use the TenantAwareFormatter

**Key Changes**:
- Logger names: `telegram_bot.{tenant_name}` for each tenant
- Format: `%(asctime)s - %(name)s - %(tenant_name)s - %(levelname)s - %(message)s`
- Automatic tenant name injection into all log records

### 3. Database Classes Updates
Updated database-related classes to use tenant-aware logging:

**Database Class** (`db/database.py`):
- Logger name: `database.{tenant_name}` for tenant-specific operations
- Logger name: `database` for global operations

**TenantDatabaseManager** (`db/tenant_manager.py`):
- Logger name: `tenant_manager` for manager operations

### 4. Multi-Tenant Bot Manager
Updated `MultiTenantBotManager` to use tenant-aware logging:
- Logger name: `multi_tenant_manager` for manager operations

### 5. Handler Classes
All handler classes automatically inherit tenant-aware logging through the Config class:

- **CommandHandlers**: Gets logger from `config.get_logger()`
- **MessageHandlers**: Gets logger from `config.get_logger()`
- **CallbackHandlers**: Gets logger from `config.get_logger()`

### 6. Utility Classes
All utility classes receive tenant-aware loggers from their parent handlers:

- **MessageHelpers**: Receives logger from handler
- **ImageManager**: Receives logger from handler
- **BotHelpers**: Receives logger from handler
- **ErrorHandler**: Receives logger from handler

### 7. Main Application Files
Updated main application files to use tenant-aware logging:

**main.py**:
- Uses TenantAwareFormatter with "GLOBAL" tenant for startup operations

**test scripts**:
- All test scripts updated to use tenant-aware logging

## Benefits

### 1. Clear Tenant Identification
Every log message clearly shows which tenant the operation belongs to:
```
2025-05-28 06:50:45,325 - handlers.CUSTOMER - CUSTOMER - INFO - Start command received from user 635834411
2025-05-28 06:50:45,325 - handlers.INDX - INDX - INFO - Start command received from user 635834411
```

### 2. Easy Debugging
When issues occur, you can immediately identify the affected tenant:
```
2025-05-28 06:50:45,325 - database.RISHUX - RISHUX - ERROR - Failed to connect to database
```

### 3. Operational Monitoring
Monitor tenant-specific operations and performance:
```
2025-05-28 06:50:45,325 - handlers.CUSTOMER - CUSTOMER - INFO - Processing access code verification for user 635834411
2025-05-28 06:50:45,325 - handlers.CUSTOMER - CUSTOMER - INFO - Access code verified successfully for user 635834411
```

### 4. Audit Trail
Complete audit trail with tenant context for compliance and troubleshooting.

## Usage

### Automatic Tenant Logging
No code changes required in most cases. The tenant-aware logging is automatically applied through:

1. **Config-based loggers**: All components that get loggers from Config automatically get tenant-aware logging
2. **Logger inheritance**: Utility classes inherit tenant-aware loggers from their parent components
3. **Formatter application**: TenantAwareFormatter is automatically applied to all handlers

### Manual Tenant Logging
For custom logging scenarios, use the utilities in `src/utils/logging_utils.py`:

```python
from src.utils.logging_utils import get_tenant_logger, log_with_tenant

# Get a tenant-aware logger
logger = get_tenant_logger("my_component", "CUSTOMER")

# Log with explicit tenant context
log_with_tenant(logger, "info", "Operation completed", "CUSTOMER")
```

## Configuration

### Default Configuration
The default logging configuration in `config/config.json` includes tenant-aware formatting:

```json
{
    "logging": {
        "log_file": "./logs/bot.log",
        "level": "INFO",
        "format": "%(asctime)s - %(name)s - %(tenant_name)s - %(levelname)s - %(message)s",
        "max_size_mb": 5,
        "backup_count": 3
    }
}
```

### Tenant-Specific Logging
Each tenant automatically gets:
- Separate logger namespace: `telegram_bot.{tenant_name}`
- Tenant name in all log messages
- Same log file (can be customized per tenant if needed)

## Testing

The implementation has been thoroughly tested with:
- Multiple tenant scenarios (CUSTOMER, INDX, RISHUX)
- All application components (Config, Database, Handlers, Utilities)
- Various log levels (INFO, WARNING, ERROR, DEBUG)
- End-to-end operation simulation

## Backward Compatibility

The implementation maintains full backward compatibility:
- Existing log parsing tools will continue to work
- Log format is extended, not changed
- No breaking changes to existing APIs
- Global operations use "GLOBAL" as tenant name

## Conclusion

The tenant-aware logging implementation provides complete visibility into multi-tenant operations while maintaining simplicity and performance. Every log message now clearly identifies the tenant context, making debugging, monitoring, and auditing much more effective.
