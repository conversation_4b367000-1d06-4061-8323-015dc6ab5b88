# YAML Configuration System Guide

## Overview

The Telegram bot now supports fully configurable menus and settings using per-customer YAML configuration files. This system allows rapid deployment of customer-specific changes without code modifications.

## Directory Structure

```
custom_menu/
├── customer_name/
│   └── customer_name.yaml
├── indx/
│   └── indx.yaml
├── rishux/
│   └── rishux.yaml
└── newcustomer/
    └── newcustomer.yaml
```

## Configuration File Structure

Each customer's YAML file contains the following sections:

### 1. Bot Information
```yaml
bot_info:
  name: "Customer Bot Name"
  description: "Bot description"
  welcome_message: "Welcome message text"
```

### 2. Main Menu Configuration
```yaml
main_menu:
  title: "Main menu message text"
  buttons:
    - text: "Button Text"
      callback_data: "callback_action"
    - text: "URL Button"
      type: "url"
      url: "https://example.com"
```

### 3. Submenu Configurations
```yaml
new_registration:
  title: "Registration menu text"
  buttons:
    - text: "Button Text"
      callback_data: "action"
```

### 4. Form Prompts
```yaml
form_prompts:
  account_number: "Enter your account number:"
  email: "Please enter your email:"
  name: "Please enter your name:"
```

### 5. Images Configuration
```yaml
images:
  welcome: "assets/welcome.webp"
  membership_offer: "assets/membership_offer.webp"
  claim_membership: "assets/claim_membership.webp"
```

### 6. External Links
```yaml
external_links:
  broker_referral: "https://broker-link.com"
  whatsapp_support: "http://wa.me/+**********"
```

### 7. Error Messages
```yaml
error_messages:
  general_error: "Something went wrong"
  invalid_code: "Invalid access code"
```

## Button Types

### Callback Buttons (Default)
```yaml
- text: "Button Text"
  callback_data: "action_name"
```

### URL Buttons
```yaml
- text: "Visit Website"
  type: "url"
  url: "https://example.com"
```

## Dynamic Content

Some content supports dynamic replacements:

- `DYNAMIC_CHANNEL_LINK` - Replaced with actual invite links
- Custom placeholders can be added as needed

## Fallback System

The system includes comprehensive fallback mechanisms:

1. **Missing YAML file**: Uses default configuration
2. **Invalid YAML syntax**: Falls back to hardcoded menus
3. **Missing sections**: Uses default values for missing parts
4. **Invalid button configuration**: Skips invalid buttons

## Adding a New Customer

1. Create directory: `custom_menu/customer_name/`
2. Create YAML file: `custom_menu/customer_name/customer_name.yaml`
3. Copy from existing customer and customize
4. Restart the bot to load new configuration

## Configuration Validation

The system validates:
- Required sections exist (`bot_info`, `main_menu`)
- Button structure is correct
- YAML syntax is valid

## Error Handling

- All errors are logged with customer context
- Invalid configurations fall back to defaults
- System continues operating even with configuration issues

## Best Practices

1. **Test configurations** before deploying
2. **Use consistent naming** for callback_data
3. **Keep messages concise** but informative
4. **Validate YAML syntax** before saving
5. **Backup configurations** before making changes

## Supported Menus

Currently configurable menus:
- `main_menu` - Main bot menu
- `new_registration` - Registration submenu
- `claim_membership` - Membership claiming
- `verify_membership` - Membership verification
- `verification_success` - Success messages
- `account_not_found` - Error messages
- `help` - Help command response
- `request_submission` - Support request flow

## Configuration Caching

- Configurations are cached for performance
- Cache is cleared when configurations are reloaded
- No restart required for configuration changes

## Troubleshooting

### Configuration Not Loading
1. Check YAML syntax
2. Verify file path and naming
3. Check bot logs for errors
4. Ensure required sections exist

### Buttons Not Working
1. Verify callback_data matches handler expectations
2. Check button structure in YAML
3. Ensure proper indentation

### Images Not Displaying
1. Verify image paths in configuration
2. Check if image files exist
3. Ensure proper file permissions

## Example Configuration

See `custom_menu/sample_customer/sample_customer.yaml` for a complete example configuration with all available options.

## Technical Implementation

The YAML configuration system consists of:

- `YAMLConfigManager`: Loads and manages configurations
- `YAMLHelpers`: Converts YAML to Telegram objects
- Integration in handlers for dynamic content
- Fallback mechanisms for reliability

## Future Enhancements

Planned features:
- Hot-reload of configurations
- Web interface for configuration management
- Configuration templates
- Advanced validation rules
- Multi-language support
