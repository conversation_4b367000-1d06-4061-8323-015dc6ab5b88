#!/usr/bin/env python3
"""
Test script for YAML Configuration System
Tests the YAML configuration loading and validation
"""

import sys
import os
import logging
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.yaml_config_manager import YAMLConfigManager
from config.config import Config

def setup_test_logging():
    """Set up logging for testing"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_yaml_config_manager():
    """Test the YAMLConfigManager directly"""
    logger = setup_test_logging()
    logger.info("Testing YAMLConfigManager...")
    
    # Initialize YAML config manager
    yaml_manager = YAMLConfigManager(logger=logger)
    
    # Test loading configurations for each customer
    customers = ['indx', 'rishux', 'newcustomer', 'sample_customer']
    
    for customer in customers:
        logger.info(f"\n--- Testing customer: {customer} ---")
        
        try:
            # Load customer configuration
            config = yaml_manager.load_customer_config(customer)
            
            if config:
                logger.info(f"✅ Successfully loaded config for {customer}")
                
                # Test main menu
                main_menu = yaml_manager.get_menu_config(customer, 'main_menu')
                if main_menu:
                    logger.info(f"✅ Main menu loaded: {len(main_menu.get('buttons', []))} buttons")
                else:
                    logger.warning(f"⚠️ No main menu found for {customer}")
                
                # Test new registration menu
                new_reg = yaml_manager.get_menu_config(customer, 'new_registration')
                if new_reg:
                    logger.info(f"✅ New registration menu loaded: {len(new_reg.get('buttons', []))} buttons")
                else:
                    logger.warning(f"⚠️ No new registration menu found for {customer}")
                
                # Test image paths
                welcome_image = yaml_manager.get_image_path(customer, 'welcome')
                if welcome_image:
                    logger.info(f"✅ Welcome image path: {welcome_image}")
                else:
                    logger.warning(f"⚠️ No welcome image path for {customer}")
                
                # Test form prompts
                account_prompt = yaml_manager.get_form_prompt(customer, 'account_number')
                logger.info(f"✅ Account prompt: {account_prompt}")
                
                # Test error messages
                error_msg = yaml_manager.get_error_message(customer, 'general_error')
                logger.info(f"✅ Error message: {error_msg}")
                
            else:
                logger.error(f"❌ Failed to load config for {customer}")
                
        except Exception as e:
            logger.error(f"❌ Error testing {customer}: {e}")

def test_config_integration():
    """Test the integration with the Config class"""
    logger = setup_test_logging()
    logger.info("\n=== Testing Config Integration ===")
    
    # Test with different tenant names
    tenants = ['indx', 'rishux', 'newcustomer']
    
    for tenant in tenants:
        logger.info(f"\n--- Testing Config integration for tenant: {tenant} ---")
        
        try:
            # Create a config instance for the tenant
            # Note: This will try to connect to MongoDB, so it might fail in test environment
            config = Config.create_for_tenant(tenant)
            
            # Test YAML methods
            main_menu_text = config.get_menu_text('main_menu')
            if main_menu_text:
                logger.info(f"✅ Main menu text loaded (length: {len(main_menu_text)})")
            else:
                logger.warning(f"⚠️ No main menu text for {tenant}")
            
            main_menu_buttons = config.get_menu_buttons('main_menu')
            if main_menu_buttons:
                logger.info(f"✅ Main menu buttons loaded: {len(main_menu_buttons)} buttons")
            else:
                logger.warning(f"⚠️ No main menu buttons for {tenant}")
            
            # Test image path
            welcome_image = config.get_image_path('welcome')
            if welcome_image:
                logger.info(f"✅ Welcome image path: {welcome_image}")
            
            # Test external link
            whatsapp_link = config.get_external_link('whatsapp_support')
            if whatsapp_link:
                logger.info(f"✅ WhatsApp link: {whatsapp_link}")
            
        except Exception as e:
            logger.warning(f"⚠️ Config integration test failed for {tenant}: {e}")
            logger.info("This is expected if MongoDB is not available")

def test_fallback_behavior():
    """Test fallback behavior for missing configurations"""
    logger = setup_test_logging()
    logger.info("\n=== Testing Fallback Behavior ===")
    
    yaml_manager = YAMLConfigManager(logger=logger)
    
    # Test with non-existent customer
    logger.info("Testing non-existent customer...")
    config = yaml_manager.load_customer_config('nonexistent_customer')
    
    if config:
        logger.info("✅ Fallback configuration loaded successfully")
        
        # Check if it has required sections
        if 'main_menu' in config and 'bot_info' in config:
            logger.info("✅ Fallback config has required sections")
        else:
            logger.error("❌ Fallback config missing required sections")
    else:
        logger.error("❌ Fallback configuration failed to load")

def test_yaml_syntax():
    """Test YAML files for syntax errors"""
    logger = setup_test_logging()
    logger.info("\n=== Testing YAML Syntax ===")
    
    import yaml
    
    config_dir = Path("custom_menu")
    if not config_dir.exists():
        logger.error("❌ custom_menu directory not found")
        return
    
    for customer_dir in config_dir.iterdir():
        if customer_dir.is_dir():
            yaml_file = customer_dir / f"{customer_dir.name}.yaml"
            if yaml_file.exists():
                logger.info(f"Testing YAML syntax for {customer_dir.name}...")
                try:
                    with open(yaml_file, 'r', encoding='utf-8') as f:
                        yaml.safe_load(f)
                    logger.info(f"✅ {customer_dir.name}.yaml syntax is valid")
                except yaml.YAMLError as e:
                    logger.error(f"❌ {customer_dir.name}.yaml has syntax error: {e}")
                except Exception as e:
                    logger.error(f"❌ Error reading {customer_dir.name}.yaml: {e}")

def main():
    """Run all tests"""
    logger = setup_test_logging()
    logger.info("🚀 Starting YAML Configuration System Tests")
    
    try:
        # Test YAML syntax first
        test_yaml_syntax()
        
        # Test YAML config manager
        test_yaml_config_manager()
        
        # Test fallback behavior
        test_fallback_behavior()
        
        # Test config integration (might fail without MongoDB)
        test_config_integration()
        
        logger.info("\n🎉 YAML Configuration System Tests Completed!")
        logger.info("Check the logs above for any issues that need attention.")
        
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
