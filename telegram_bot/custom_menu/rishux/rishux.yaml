# RISHUX Customer Configuration
# This file defines all configurable elements for the RISHUX Telegram bot

# Basic bot information
bot_info:
  name: "RISHUX Trading Bot"
  description: "Advanced Trading Platform for RISHUX Members"
  welcome_message: "Welcome to RISHUX Trading Platform ✅ \n\nYour gateway to professional Forex trading excellence 📈"

# Main menu configuration
main_menu:
  title: "Welcome to RISHUX Trading Platform ✅ \n\nYour gateway to professional Forex trading excellence 📈"
  buttons:
    - text: "New Registration 🧑‍💻"
      callback_data: "new_registration"
    - text: "Claim Membership 🎖️"
      callback_data: "claim_membership"
    - text: "Verify Membership 🔃"
      callback_data: "verify_membership"
    - text: "Withdrawals & Deposits 💱"
      callback_data: "wd_start"
    - text: "Submit a Request 📪"
      callback_data: "submit_request_start"

# New Registration submenu
new_registration:
  title: |
    🌟 Join RISHUX Premium Trading Community - FREE Access! 🌟

    Unlock exclusive benefits:
    🎯 Expert Trading Signals & Analysis
    📊 Real-time Market Updates & Charts
    📹 Comprehensive Trading Education
    📰 Market News & Economic Calendar
    🥇 Priority Customer Support
    🤝 Dedicated Account Management
  buttons:
    - text: "Join RISHUX Community 🏌️"
      callback_data: "reg_want_to_join"
    - text: "Account Created ✅"
      callback_data: "account_created"
    - text: "Existing Account Holder 🧾"
      callback_data: "partner_change_start"
    - text: "Need Assistance 🪃"
      callback_data: "submit_request_start"
    - text: "Back to Main Menu"
      callback_data: "back_to_menu"

# Claim Membership submenu
claim_membership:
  title: "🎖️ Please enter your RISHUX access code to activate your membership:"
  buttons:
    - text: "Back to Main Menu"
      callback_data: "back_to_menu"

# Verify Membership submenu
verify_membership:
  title: "🔃 Please enter your trading account number for RISHUX verification:"
  buttons:
    - text: "Back to Main Menu"
      callback_data: "back_to_menu"

# Verification success messages
verification_success:
  title: "🎊 Congratulations! Your RISHUX profile is verified ✅ Welcome to our Premium Community! 🥷"
  buttons:
    - text: "Access RISHUX Premium 🏆"
      type: "url"
      url: "DYNAMIC_CHANNEL_LINK"
    - text: "Explore Premium Features 🌍"
      callback_data: "verify_explore_offerings"
    - text: "My RISHUX Status 🏅"
      callback_data: "community_ranking"
    - text: "Contact RISHUX Support 🔰"
      type: "url"
      url: "http://wa.me/+************"
    - text: "Need More Support?"
      callback_data: "submit_request_start"
    - text: "Back to Main Menu"
      callback_data: "back_to_menu"

# Account not found messages
account_not_found:
  title: "⚠️ Sorry, we could not locate your RISHUX registration 💀"
  buttons:
    - text: "Apply for RISHUX Membership 🏆"
      callback_data: "verify_apply_new"
    - text: "Manual Verification Request 🔍"
      callback_data: "verification_request_start"
    - text: "Update Partner/IB Details 🥷"
      callback_data: "partner_change_start"
    - text: "Back to Menu"
      callback_data: "back_to_menu"

# Help command configuration
help:
  title: |
    🔹 *RISHUX Trading Platform Commands* 🔹

    /start - Start the bot and access main menu
    /help - Display this help information
    /menu - Return to main menu

    💬 Contact RISHUX support for personalized assistance!
  buttons:
    - text: "Main Menu"
      callback_data: "back_to_menu"

# Request submission flow
request_submission:
  initial_prompt: "Your RISHUX support request has been logged. How would you like to proceed?"
  buttons:
    - text: "Add Additional Details ℹ️"
      callback_data: "support_add_details"
    - text: "Submit to RISHUX Team 🏁"
      callback_data: "support_finalize_request"
    - text: "Back to Main Menu"
      callback_data: "back_to_menu"

# Form prompts
form_prompts:
  account_number: "Enter your RISHUX trading account number:"
  email: "Please provide your email for RISHUX communications:"
  whatsapp: "Please enter your WhatsApp number for RISHUX updates:"
  name: "Please enter your full name for RISHUX registration:"
  trading_experience: "Please describe your trading experience:"
  request_text: "Please describe your RISHUX support inquiry:"

# Images configuration
images:
  welcome: "assets/welcome.webp"
  membership_offer: "assets/membership_offer.webp"
  claim_membership: "assets/claim_membership.webp"
  verify_membership: "assets/verify_membership.webp"
  withdrawal: "assets/withdrawal.webp"
  support: "assets/support.webp"
  account_number: "assets/account_number_image.png"

# External URLs and links
external_links:
  broker_referral: "https://one.exness-track.com/a/6w8z8ggx"
  whatsapp_support: "http://wa.me/+************"
  withdrawal_team: "http://wa.me/+************"

# PDF files
pdf_files:
  simple_steps: "assets/simple_steps_exness.pdf"

# Error messages
error_messages:
  general_error: "Sorry, RISHUX services are temporarily unavailable. Please try again later."
  invalid_code: "Invalid RISHUX access code. Please verify and try again."
  account_not_found: "RISHUX account not found in our database."
  pdf_not_found: "RISHUX documentation not available at the moment."

# Success messages
success_messages:
  registration_complete: "RISHUX registration completed successfully!"
  request_submitted: "Your RISHUX support request has been submitted successfully."
  verification_complete: "RISHUX verification completed successfully!"
