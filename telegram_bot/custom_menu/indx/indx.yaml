# INDX Customer Configuration Framework
# This file defines the complete menu structure and logic for INDX Telegram bot

# Basic bot information
bot_info:
  name: "INDX Trading Bot"
  description: "Professional Trading Verification Junction for INDX"
  welcome_message: "Welcome to INDX Trading Junction ✅ \n\nUnlock the potential of expert-led Forex trading world 📈"

# Main menu configuration with hierarchical structure
main_menu:
  title: "Welcome to Trading Junction ✅ \n\nUnlock the potential of expert-led Forex trading world 📈"
  image: "welcome"
  buttons:
    new_registration:
      title: "New Registration 🧑‍💻"
      image: "membership_offer"
      submenu:
        title: |
          🚀 Join INDX Trading Elite Membership - Completely FREE! 🚀

          Get exclusive access to:
          ✅ Daily Premium Trading Signals 🎯
          ✅ Live Market Analysis & Chart Updates 📊
          ✅ Professional Forex Trading Course 📹
          ✅ Real-time News & Economic Data 📰
          ✅ Priority Account Support 🥇
          ✅ VIP Withdrawal & Deposit Assistance 🤝
        buttons:
          want_to_join:
            title: "I Want to Join INDX Elite 🏌️"
            logic_function: "handle_registration_flow"
          account_created:
            title: "Account Created ✅"
            logic_function: "handle_account_verification"
          existing_account:
            title: "I Already have an Account 🧾"
            submenu:
              title: "Please select your account type:"
              buttons:
                partner_change:
                  title: "Change Partner/IB 🥷"
                  logic_function: "handle_partner_change"
                account_issues:
                  title: "Account Issues 🔧"
                  logic_function: "handle_account_issues"
                back_to_main:
                  title: "Back to Main Menu"
                  action: "back_to_menu"
          need_help:
            title: "Need Expert Help 🪃"
            action: "submit_request_start"
          back_to_main:
            title: "Back to Main Menu"
            action: "back_to_menu"

    claim_membership:
      title: "Claim Membership 🎖️"
      image: "claim_membership"
      submenu:
        title: "🎖️ Enter your INDX access code to claim your elite membership:"
        logic_function: "handle_membership_claim"
        buttons:
          back_to_main:
            title: "Back to Main Menu"
            action: "back_to_menu"

    verify_membership:
      title: "Verify Membership 🔃"
      image: "verify_membership"
      submenu:
        title: "🔃 Enter your trading account number to verify your INDX membership:"
        logic_function: "handle_membership_verification"
        buttons:
          back_to_main:
            title: "Back to Main Menu"
            action: "back_to_menu"

    withdrawals_deposits:
      title: "Withdrawals & Deposits 💱"
      image: "withdrawal"
      submenu:
        title: "💱 INDX Withdrawal & Deposit Services"
        buttons:
          withdrawal_help:
            title: "Withdrawal Assistance 💸"
            logic_function: "handle_withdrawal_request"
          deposit_help:
            title: "Deposit Support 💰"
            logic_function: "handle_deposit_request"
          contact_team:
            title: "Contact INDX Support Team �"
            action: "external_link"
            url: "http://wa.me/+************"
          back_to_main:
            title: "Back to Main Menu"
            action: "back_to_menu"

    submit_request:
      title: "Submit a Request 📪"
      image: "support"
      submenu:
        title: "📪 INDX Support Request"
        logic_function: "handle_support_request"
        buttons:
          back_to_main:
            title: "Back to Main Menu"
            action: "back_to_menu"

# New Registration submenu
new_registration:
  title: |
    🚀 Join INDX Trading Elite Membership - Completely FREE! 🚀

    Get exclusive access to:
    ✅ Daily Premium Trading Signals 🎯
    ✅ Live Market Analysis & Chart Updates 📊
    ✅ Professional Forex Trading Course 📹
    ✅ Real-time News & Economic Data 📰
    ✅ Priority Account Support 🥇
    ✅ VIP Withdrawal & Deposit Assistance 🤝
  buttons:
    - text: "I Want to Join INDX Elite 🏌️"
      callback_data: "reg_want_to_join"
    - text: "Account Created ✅"
      callback_data: "account_created"
    - text: "I Already have an Account 🧾"
      callback_data: "partner_change_start"
    - text: "Need Expert Help 🪃"
      callback_data: "submit_request_start"
    - text: "Back to Main Menu"
      callback_data: "back_to_menu"

# Claim Membership submenu
claim_membership:
  title: "🎖️ Enter your INDX access code to claim your elite membership:"
  buttons:
    - text: "Back to Main Menu"
      callback_data: "back_to_menu"

# Verify Membership submenu
verify_membership:
  title: "🔃 Enter your trading account number to verify your INDX membership:"
  buttons:
    - text: "Back to Main Menu"
      callback_data: "back_to_menu"

# Verification success messages
verification_success:
  title: "🎉 Congratulations! Your INDX profile is verified ✅ You are now an Elite Member! 🥷"
  buttons:
    - text: "Access INDX Premium Space 🏆"
      type: "url"
      url: "DYNAMIC_CHANNEL_LINK"
    - text: "Explore Elite Benefits 🌍"
      callback_data: "verify_explore_offerings"
    - text: "My INDX Ranking 🏅"
      callback_data: "community_ranking"
    - text: "Contact INDX Support Team 🔰"
      type: "url"
      url: "http://wa.me/+************"
    - text: "Need More Support?"
      callback_data: "submit_request_start"
    - text: "Back to Main Menu"
      callback_data: "back_to_menu"

# Account not found messages
account_not_found:
  title: "❌ Sorry, we could not find your INDX registration 💀"
  buttons:
    - text: "Apply for INDX Membership 🏆"
      callback_data: "verify_apply_new"
    - text: "Submit Manual Verification 🔍"
      callback_data: "verification_request_start"
    - text: "Change your Partner/IB 🥷"
      callback_data: "partner_change_start"
    - text: "Back to Menu"
      callback_data: "back_to_menu"

# Help command configuration
help:
  title: |
    🔹 *INDX Trading Bot Commands* 🔹

    /start - Start the bot and show the main menu
    /help - Show this help message
    /menu - Show the main menu

    📞 For immediate support, contact our INDX team!
  buttons:
    - text: "Main Menu"
      callback_data: "back_to_menu"

# Request submission flow
request_submission:
  initial_prompt: "Your INDX support request has been recorded. What would you like to do next?"
  buttons:
    - text: "Add more Details ℹ️"
      callback_data: "support_add_details"
    - text: "Submit to INDX Team 🏁"
      callback_data: "support_finalize_request"
    - text: "Back to Main Menu"
      callback_data: "back_to_menu"

# Form prompts
form_prompts:
  account_number: "Enter your INDX trading account number:"
  email: "Please enter your email address for INDX communications:"
  whatsapp: "Please enter your WhatsApp number for INDX support:"
  name: "Please enter your full name for INDX registration:"
  trading_experience: "Please describe your trading experience with INDX:"
  request_text: "Please describe your INDX support request:"

# Images configuration
images:
  welcome: "assets/welcome.webp"
  membership_offer: "assets/membership_offer.webp"
  claim_membership: "assets/claim_membership.webp"
  verify_membership: "assets/verify_membership.webp"
  withdrawal: "assets/withdrawal.webp"
  support: "assets/support.webp"
  account_number: "assets/account_number_image.png"

# External URLs and links
external_links:
  broker_referral: "https://one.exness-track.com/a/6w8z8ggx"
  whatsapp_support: "http://wa.me/+************"
  withdrawal_team: "http://wa.me/+************"

# PDF files
pdf_files:
  simple_steps: "assets/simple_steps_exness.pdf"

# Error messages
error_messages:
  general_error: "Sorry, something went wrong with INDX services. Please try again later."
  invalid_code: "Invalid INDX access code. Please try again."
  account_not_found: "INDX account not found in our system."
  pdf_not_found: "INDX PDF file not available at the moment."

# Success messages
success_messages:
  registration_complete: "INDX registration completed successfully!"
  request_submitted: "Your INDX support request has been submitted successfully."
  verification_complete: "INDX verification completed successfully!"
