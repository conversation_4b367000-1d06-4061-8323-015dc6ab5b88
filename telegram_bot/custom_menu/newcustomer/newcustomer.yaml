# New Customer Configuration
# This file defines all configurable elements for the New Customer Telegram bot

# Basic bot information
bot_info:
  name: "New Customer Trading Bot"
  description: "Welcome to Your Trading Journey"
  welcome_message: "Welcome to your Trading Journey ✅ \n\nDiscover the world of professional Forex trading 📈"

# Main menu configuration
main_menu:
  title: "Welcome to your Trading Journey ✅ \n\nDiscover the world of professional Forex trading 📈"
  buttons:
    - text: "New Registration 🧑‍💻"
      callback_data: "new_registration"
    - text: "Claim Membership 🎖️"
      callback_data: "claim_membership"
    - text: "Verify Membership 🔃"
      callback_data: "verify_membership"
    - text: "Withdrawals & Deposits 💱"
      callback_data: "wd_start"
    - text: "Submit a Request 📪"
      callback_data: "submit_request_start"

# New Registration submenu
new_registration:
  title: |
    🎯 Start Your Trading Journey - Free Membership Available! 🎯

    What you'll get:
    📈 Professional Trading Signals
    📊 Market Analysis & Updates
    📚 Educational Trading Resources
    📰 Market News & Insights
    🎯 Personalized Support
    💼 Account Management Assistance
  buttons:
    - text: "Start My Journey 🏌️"
      callback_data: "reg_want_to_join"
    - text: "Account Created ✅"
      callback_data: "account_created"
    - text: "I Have an Account 🧾"
      callback_data: "partner_change_start"
    - text: "Need Help 🪃"
      callback_data: "submit_request_start"
    - text: "Back to Main Menu"
      callback_data: "back_to_menu"

# Claim Membership submenu
claim_membership:
  title: "🎖️ Please enter your access code to claim your membership:"
  buttons:
    - text: "Back to Main Menu"
      callback_data: "back_to_menu"

# Verify Membership submenu
verify_membership:
  title: "🔃 Please enter your account number to verify your membership:"
  buttons:
    - text: "Back to Main Menu"
      callback_data: "back_to_menu"

# Verification success messages
verification_success:
  title: "🎉 Congratulations! Your profile is verified ✅ Welcome to our trading community! 🥷"
  buttons:
    - text: "Access Premium Features 🏆"
      type: "url"
      url: "DYNAMIC_CHANNEL_LINK"
    - text: "Explore Benefits 🌍"
      callback_data: "verify_explore_offerings"
    - text: "My Status 🏅"
      callback_data: "community_ranking"
    - text: "Contact Support Team 🔰"
      type: "url"
      url: "http://wa.me/+************"
    - text: "Need More Support?"
      callback_data: "submit_request_start"
    - text: "Back to Main Menu"
      callback_data: "back_to_menu"

# Account not found messages
account_not_found:
  title: "❌ Sorry, we could not find your registration with us 💀"
  buttons:
    - text: "Apply for Membership 🏆"
      callback_data: "verify_apply_new"
    - text: "Manual Verification 🔍"
      callback_data: "verification_request_start"
    - text: "Change Partner/IB 🥷"
      callback_data: "partner_change_start"
    - text: "Back to Menu"
      callback_data: "back_to_menu"

# Help command configuration
help:
  title: |
    🔹 *Available Commands* 🔹

    /start - Start the bot and show the main menu
    /help - Show this help message
    /menu - Show the main menu

    📞 Contact our support team for assistance!
  buttons:
    - text: "Main Menu"
      callback_data: "back_to_menu"

# Request submission flow
request_submission:
  initial_prompt: "Your request has been recorded. What would you like to do next?"
  buttons:
    - text: "Add more Details ℹ️"
      callback_data: "support_add_details"
    - text: "Submit Request 🏁"
      callback_data: "support_finalize_request"
    - text: "Back to Main Menu"
      callback_data: "back_to_menu"

# Form prompts
form_prompts:
  account_number: "Enter your trading account number:"
  email: "Please enter your email address:"
  whatsapp: "Please enter your WhatsApp number:"
  name: "Please enter your full name:"
  trading_experience: "Please describe your trading experience:"
  request_text: "Please describe your request or issue:"

# Images configuration
images:
  welcome: "assets/welcome.webp"
  membership_offer: "assets/membership_offer.webp"
  claim_membership: "assets/claim_membership.webp"
  verify_membership: "assets/verify_membership.webp"
  withdrawal: "assets/withdrawal.webp"
  support: "assets/support.webp"
  account_number: "assets/account_number_image.png"

# External URLs and links
external_links:
  broker_referral: "https://one.exness-track.com/a/6w8z8ggx"
  whatsapp_support: "http://wa.me/+************"
  withdrawal_team: "http://wa.me/+************"

# PDF files
pdf_files:
  simple_steps: "assets/simple_steps_exness.pdf"

# Error messages
error_messages:
  general_error: "Sorry, something went wrong. Please try again later."
  invalid_code: "Invalid access code. Please try again."
  account_not_found: "Account not found in our system."
  pdf_not_found: "PDF file not available at the moment."

# Success messages
success_messages:
  registration_complete: "Registration completed successfully!"
  request_submitted: "Your request has been submitted successfully."
  verification_complete: "Verification completed successfully!"
