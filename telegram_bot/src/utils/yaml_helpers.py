"""
YAML Configuration Helper Utilities
Provides helper functions for working with YAML-based configurations
"""

import logging
from typing import List, Dict, Any, Optional
from telegram import Inline<PERSON>eyboardButton, InlineKeyboardMarkup


class YAMLHelpers:
    """
    Helper class for working with YAML configurations in Telegram bot context.
    
    This class provides utilities for:
    - Converting YAML button configurations to Telegram keyboard objects
    - Processing dynamic content in YAML configurations
    - Handling fallbacks and error cases
    """
    
    def __init__(self, config, logger: Optional[logging.Logger] = None):
        """
        Initialize YAML helpers.
        
        Args:
            config: Config instance with YAML configuration access
            logger: Optional logger instance
        """
        self.config = config
        self.logger = logger or logging.getLogger(__name__)
    
    def create_keyboard_from_yaml(self, menu_name: str, dynamic_replacements: Optional[Dict[str, str]] = None) -> Optional[InlineKeyboardMarkup]:
        """
        Create a Telegram InlineKeyboardMarkup from YAML button configuration.
        
        Args:
            menu_name: Name of the menu in YAML config
            dynamic_replacements: Optional dictionary of dynamic content replacements
            
        Returns:
            InlineKeyboardMarkup object or None if configuration not found
        """
        try:
            # Get button configuration from YAML
            buttons_config = self.config.get_menu_buttons(menu_name)
            
            if not buttons_config:
                self.logger.warning(f"No button configuration found for menu: {menu_name}")
                return None
            
            # Convert YAML buttons to Telegram buttons
            keyboard_rows = []
            current_row = []
            
            for button_config in buttons_config:
                button = self._create_button_from_config(button_config, dynamic_replacements)
                if button:
                    current_row.append(button)
                    
                    # Check if this button should be on its own row (default behavior)
                    # You can extend this logic to support multi-button rows from YAML config
                    if len(current_row) >= 1:  # One button per row by default
                        keyboard_rows.append(current_row)
                        current_row = []
            
            # Add any remaining buttons
            if current_row:
                keyboard_rows.append(current_row)
            
            if keyboard_rows:
                return InlineKeyboardMarkup(keyboard_rows)
            else:
                self.logger.warning(f"No valid buttons created for menu: {menu_name}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error creating keyboard from YAML for menu {menu_name}: {e}")
            return None
    
    def _create_button_from_config(self, button_config: Dict[str, Any], dynamic_replacements: Optional[Dict[str, str]] = None) -> Optional[InlineKeyboardButton]:
        """
        Create a single InlineKeyboardButton from YAML configuration.
        
        Args:
            button_config: Button configuration dictionary from YAML
            dynamic_replacements: Optional dictionary of dynamic content replacements
            
        Returns:
            InlineKeyboardButton object or None if invalid configuration
        """
        try:
            # Extract button text
            text = button_config.get('text', '')
            if not text:
                self.logger.warning("Button configuration missing 'text' field")
                return None
            
            # Apply dynamic replacements if provided
            if dynamic_replacements:
                for placeholder, replacement in dynamic_replacements.items():
                    text = text.replace(placeholder, replacement)
            
            # Determine button type and create appropriate button
            button_type = button_config.get('type', 'callback')
            
            if button_type == 'url':
                # URL button
                url = button_config.get('url', '')
                if not url:
                    self.logger.warning("URL button configuration missing 'url' field")
                    return None
                
                # Apply dynamic replacements to URL if provided
                if dynamic_replacements:
                    for placeholder, replacement in dynamic_replacements.items():
                        url = url.replace(placeholder, replacement)
                
                return InlineKeyboardButton(text, url=url)
            
            elif button_type == 'callback' or 'callback_data' in button_config:
                # Callback button (default type)
                callback_data = button_config.get('callback_data', '')
                if not callback_data:
                    self.logger.warning("Callback button configuration missing 'callback_data' field")
                    return None
                
                return InlineKeyboardButton(text, callback_data=callback_data)
            
            else:
                self.logger.warning(f"Unknown button type: {button_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error creating button from config: {e}")
            return None
    
    def get_message_text_with_replacements(self, menu_name: str, dynamic_replacements: Optional[Dict[str, str]] = None) -> str:
        """
        Get message text from YAML config with dynamic replacements.
        
        Args:
            menu_name: Name of the menu in YAML config
            dynamic_replacements: Optional dictionary of dynamic content replacements
            
        Returns:
            Message text with replacements applied
        """
        try:
            # Get message text from YAML
            text = self.config.get_menu_text(menu_name)
            
            if not text:
                self.logger.warning(f"No message text found for menu: {menu_name}")
                return ""
            
            # Apply dynamic replacements if provided
            if dynamic_replacements:
                for placeholder, replacement in dynamic_replacements.items():
                    text = text.replace(placeholder, replacement)
            
            return text
            
        except Exception as e:
            self.logger.error(f"Error getting message text for menu {menu_name}: {e}")
            return ""
    
    def create_menu_response(self, menu_name: str, dynamic_replacements: Optional[Dict[str, str]] = None) -> tuple[str, Optional[InlineKeyboardMarkup]]:
        """
        Create a complete menu response with text and keyboard from YAML config.
        
        Args:
            menu_name: Name of the menu in YAML config
            dynamic_replacements: Optional dictionary of dynamic content replacements
            
        Returns:
            Tuple of (message_text, keyboard_markup)
        """
        try:
            # Get message text
            message_text = self.get_message_text_with_replacements(menu_name, dynamic_replacements)
            
            # Get keyboard
            keyboard = self.create_keyboard_from_yaml(menu_name, dynamic_replacements)
            
            return message_text, keyboard
            
        except Exception as e:
            self.logger.error(f"Error creating menu response for {menu_name}: {e}")
            return "", None
    
    def get_image_path_for_menu(self, menu_name: str) -> Optional[str]:
        """
        Get the appropriate image path for a menu based on naming conventions.
        
        Args:
            menu_name: Name of the menu
            
        Returns:
            Image path string or None if not found
        """
        try:
            # Map menu names to image names
            menu_to_image_map = {
                'main_menu': 'welcome',
                'new_registration': 'membership_offer',
                'claim_membership': 'claim_membership',
                'verify_membership': 'verify_membership',
                'wd_start': 'withdrawal',
                'submit_request_start': 'support'
            }
            
            image_name = menu_to_image_map.get(menu_name, menu_name)
            return self.config.get_image_path(image_name)
            
        except Exception as e:
            self.logger.error(f"Error getting image path for menu {menu_name}: {e}")
            return None
    
    def validate_yaml_config(self, customer_name: str) -> bool:
        """
        Validate that the YAML configuration for a customer is complete and valid.
        
        Args:
            customer_name: Name of the customer
            
        Returns:
            True if configuration is valid, False otherwise
        """
        try:
            # Get the full configuration
            config = self.config.get_yaml_config()
            
            if not config:
                self.logger.error(f"No YAML configuration found for customer: {customer_name}")
                return False
            
            # Check required sections
            required_sections = ['bot_info', 'main_menu']
            for section in required_sections:
                if section not in config:
                    self.logger.error(f"Missing required section '{section}' in YAML config for customer: {customer_name}")
                    return False
            
            # Validate main menu structure
            main_menu = config.get('main_menu', {})
            if 'title' not in main_menu or 'buttons' not in main_menu:
                self.logger.error(f"Invalid main_menu structure for customer: {customer_name}")
                return False
            
            # Validate buttons
            buttons = main_menu.get('buttons', [])
            if not isinstance(buttons, list) or len(buttons) == 0:
                self.logger.error(f"Invalid or empty buttons list for customer: {customer_name}")
                return False
            
            for i, button in enumerate(buttons):
                if not isinstance(button, dict):
                    self.logger.error(f"Button {i} is not a dictionary for customer: {customer_name}")
                    return False
                
                if 'text' not in button:
                    self.logger.error(f"Button {i} missing 'text' field for customer: {customer_name}")
                    return False
                
                if 'callback_data' not in button and 'url' not in button:
                    self.logger.error(f"Button {i} missing 'callback_data' or 'url' field for customer: {customer_name}")
                    return False
            
            self.logger.info(f"YAML configuration validation passed for customer: {customer_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error validating YAML config for customer {customer_name}: {e}")
            return False
    
    def get_fallback_menu(self, menu_name: str) -> tuple[str, Optional[InlineKeyboardMarkup]]:
        """
        Get a fallback menu when YAML configuration is not available.
        
        Args:
            menu_name: Name of the menu
            
        Returns:
            Tuple of (message_text, keyboard_markup) with fallback content
        """
        # Provide basic fallback menus
        if menu_name == 'main_menu':
            text = "Welcome to the verification Junction ✅ \n\nUnlock the potential of expert-led Forex trading world 📈"
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("New Registration 🧑‍💻", callback_data='new_registration')],
                [InlineKeyboardButton("Claim Membership 🎖️", callback_data='claim_membership')],
                [InlineKeyboardButton("Verify Membership 🔃", callback_data='verify_membership')],
                [InlineKeyboardButton("Withdrawals & Deposits 💱", callback_data='wd_start')],
                [InlineKeyboardButton("Submit a Request 📪", callback_data='submit_request_start')]
            ])
            return text, keyboard
        
        # Default fallback
        return "Menu not available", InlineKeyboardMarkup([[InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]])
