from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes, ConversationHandler
from telegram.error import TelegramError

# Import new utility classes
from src.utils.image_manager import ImageManager
from src.utils.message_helpers import MessageHelpers
from src.utils.bot_helpers import BotHelpers
from src.utils.error_handler import <PERSON><PERSON>r<PERSON>and<PERSON>
from src.utils.yaml_helpers import Y<PERSON><PERSON><PERSON>elpers

# Define conversation states
WAITING_FOR_CODE, HELP_ROUTE = range(2)

class CommandHandlers:
    """Handlers for bot commands"""

    def __init__(self, config, subscription_manager):
        self.config = config
        self.logger = config.get_logger()
        self.sub_manager = subscription_manager

        # Initialize utility classes
        self.image_manager = ImageManager(self.logger)
        self.message_helpers = MessageHelpers(self.logger)
        self.bot_helpers = BotHelpers(config, self.logger)
        self.error_handler = <PERSON>rror<PERSON>andler(self.logger)
        self.yaml_helpers = YAMLHelpers(config, self.logger)

        # We no longer store a static channel_link
        # Instead, we'll generate one-time use links when needed
        self.logger.info("CommandHandlers initialized with utility classes - will generate one-time use channel links when needed")

    async def start(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle the /start or /new command"""
        try:
            user_info = self.message_helpers.extract_user_info(update)
            user_id = user_info.get('user_id')
            username = user_info.get('username', 'Unknown')

            self.logger.info(f"Start command received from user {user_id} (@{username})")

            # Clear any existing conversation state
            context.user_data.clear()

            # Use the image manager to check for welcome image
            welcome_image_path = self.image_manager.get_image_path('welcome')

            # Pass the welcome image path directly to the method
            return await self.show_menu_by_subscription(update.message, user_id, is_edit=False, welcome_image_path=welcome_image_path)
        except Exception as e:
            self.error_handler.log_operation_failure("start_command", e, user_id)
            await self.message_helpers.send_error_message(update.message)
            return ConversationHandler.END

    async def get_subscription_menu(self):
        """Get the standard subscription menu keyboard and text from YAML configuration"""
        try:
            # Try to get menu from YAML configuration
            text, keyboard = self.yaml_helpers.create_menu_response('main_menu')

            if text and keyboard:
                self.logger.debug("Successfully loaded main menu from YAML configuration")
                return keyboard, text
            else:
                self.logger.warning("Failed to load main menu from YAML, using fallback")
                # Fallback to hardcoded menu
                return self._get_fallback_subscription_menu()

        except Exception as e:
            self.logger.error(f"Error loading subscription menu from YAML: {e}")
            # Fallback to hardcoded menu
            return self._get_fallback_subscription_menu()

    def _get_fallback_subscription_menu(self):
        """Get fallback subscription menu when YAML config is not available"""
        keyboard = [
            [InlineKeyboardButton("New Registration 🧑‍💻", callback_data='new_registration')],
            [InlineKeyboardButton("Claim Membership 🎖️", callback_data='claim_membership')],
            [InlineKeyboardButton("Verify Membership 🔃", callback_data='verify_membership')],
            [InlineKeyboardButton("Withdrawals & Deposits 💱", callback_data='wd_start')],
            [InlineKeyboardButton("Submit a Request 📪", callback_data='submit_request_start')]
        ]

        text = (
            "Welcome to the verification Junction ✅ \n\n"
            "Unlock the potential of expert-led Forex trading world 📈"
        )

        return InlineKeyboardMarkup(keyboard), text

    async def show_menu_by_subscription(self, message, user_id, is_edit=True, welcome_image_path=None):
        """Show subscription menu for all users when /start is executed"""
        self.logger.debug(f"Showing menu for user {user_id}, is_edit={is_edit}")

        # Always show the subscription menu regardless of user's subscription status
        self.logger.info(f"Showing subscription menu to user {user_id}")

        # Get the standard menu keyboard and text
        reply_markup, text = await self.get_subscription_menu()

        try:
            if is_edit:
                self.logger.debug(f"Editing message for user {user_id}")
                # Use the message helpers for safe editing
                success = await self.message_helpers.edit_message_safely(message, text, reply_markup)
                if not success:
                    self.logger.warning(f"Failed to edit message for user {user_id}")
            else:
                self.logger.debug(f"Sending new message for user {user_id}")

                # If we have a welcome image and this is a new message (not edit), send with photo
                if welcome_image_path and not is_edit:
                    # Use the image manager to send with welcome image
                    await self.image_manager.send_message_with_image(message, text, 'welcome', reply_markup)
                else:
                    # Otherwise just send text message
                    if welcome_image_path:
                        self.logger.info(f"Not sending image because is_edit={is_edit}")
                    else:
                        self.logger.info("No welcome image path provided")
                    await message.reply_text(text, reply_markup=reply_markup)
            return WAITING_FOR_CODE
        except Exception as e:
            self.error_handler.log_operation_failure("show_menu", e, user_id)
            return WAITING_FOR_CODE

    # Add this method to your CommandHandlers class

    async def cancel(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Cancel the current conversation."""
        user = update.message.from_user
        self.logger.info(f"User {user.id} canceled the conversation.")

        await update.message.reply_text(
            "Operation cancelled. You can start again with /start command."
        )
        return ConversationHandler.END

    async def help(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle the /help command"""
        try:
            user_info = self.message_helpers.extract_user_info(update)
            user_id = user_info.get('user_id')
            username = user_info.get('username', 'Unknown')

            self.logger.info(f"Help command received from user {user_id} (@{username})")

            # Try to get help message from YAML configuration
            try:
                message_text, keyboard = self.yaml_helpers.create_menu_response('help')

                if not message_text or not keyboard:
                    # Fallback to hardcoded help message
                    message_text = (
                        "🔹 *Available Commands* 🔹\n\n"
                        "/start - Start the bot and show the main menu\n"
                        "/help - Show this help message\n"
                        "/menu - Show the main menu\n"
                    )
                    keyboard = InlineKeyboardMarkup([
                        [InlineKeyboardButton("Main Menu", callback_data="back_to_menu")]
                    ])

            except Exception as e:
                self.logger.warning(f"Failed to load help from YAML config: {e}")
                # Fallback to hardcoded help message
                message_text = (
                    "🔹 *Available Commands* 🔹\n\n"
                    "/start - Start the bot and show the main menu\n"
                    "/help - Show this help message\n"
                    "/menu - Show the main menu\n"
                )
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton("Main Menu", callback_data="back_to_menu")]
                ])

            await update.message.reply_text(
                message_text,
                reply_markup=keyboard,
                parse_mode='Markdown'
            )

            return HELP_ROUTE
        except Exception as e:
            self.error_handler.log_operation_failure("help_command", e, user_id)
            await self.message_helpers.send_error_message(update.message)
            return ConversationHandler.END

    async def create_one_time_invite_link(self, context):
        """Create a one-time use invite link for the channel"""
        try:
            # Get chat_id from config
            chat_id_str = self.config.get('channel_id')
            if not chat_id_str:
                self.logger.error("Channel ID not found in configuration.")
                # Return a fallback link
                return self.config.get('channel_link', 'https://t.me/your_channel')

            # Format the chat_id properly for Telegram API
            # For channels and supergroups, the ID must start with -100
            # If it already has -100, keep it, otherwise add it
            if not chat_id_str.startswith('-100'):
                chat_id_str = f"-100{chat_id_str}"

            # Convert to integer
            chat_id = int(chat_id_str)
            self.logger.info(f"Attempting to create invite link for chat_id: {chat_id}")

            invite_link_obj = await context.bot.create_chat_invite_link(
                chat_id=chat_id,
                member_limit=1,  # CRUCIAL: Limits the link to exactly one use
                expire_date=None  # No expiration date, but it will expire after one use
            )
            self.logger.info(f"Created one-time invite link: {invite_link_obj.invite_link}")
            return invite_link_obj.invite_link

        except (TelegramError, ValueError, TypeError) as e:
            # Catch specific errors: Telegram issues, ValueError/TypeError if chat_id is invalid format
            self.logger.error(f"Error creating invite link for chat {chat_id_str if 'chat_id_str' in locals() else 'unknown'}: {e}")
            # Try with a different format as fallback
            try:
                if 'chat_id_str' in locals() and chat_id_str.startswith('-100'):
                    # Try without the -100 prefix
                    chat_id = int(chat_id_str.replace('-100', ''))
                    self.logger.info(f"Retrying with alternative chat_id format: {chat_id}")
                    invite_link_obj = await context.bot.create_chat_invite_link(
                        chat_id=chat_id,
                        member_limit=1,
                        expire_date=None
                    )
                    self.logger.info(f"Created one-time invite link with alternative format: {invite_link_obj.invite_link}")
                    return invite_link_obj.invite_link
            except Exception as retry_error:
                self.logger.error(f"Retry also failed: {retry_error}")

            # Return a fallback link
            return self.config.get('channel_link', 'https://t.me/your_channel')
        except Exception as e:
            self.logger.error(f"Unexpected error creating invite link: {e}")
            # Return a fallback link
            return self.config.get('channel_link', 'https://t.me/your_channel')
